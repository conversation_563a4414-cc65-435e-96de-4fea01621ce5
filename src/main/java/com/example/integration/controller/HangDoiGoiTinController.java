package com.example.integration.controller;

import com.example.integration.config.PortalUtil;
import com.example.integration.config.PropKey;
import com.example.integration.constant.Constant;
import com.example.integration.dto.req.DonViLienThongReqDTO;
import com.example.integration.dto.req.HangDoiGoiTinReqDTO;
import com.example.integration.dto.req.HangDoiGoiTinTrangThaiReqDTO;
import com.example.integration.dto.resp.BaseRespDTO;
import com.example.integration.dto.resp.GetAgencyRespDTO;
import com.example.integration.dto.resp.HangDoiGoiTinRespDTO;
import com.example.integration.entity.*;
import com.example.integration.entity.ext.HeThongKetNoiExt;
import com.example.integration.hub.action.DonViLienThongAction;
import com.example.integration.hub.action.HangDoiGoiTinAction;
import com.example.integration.hub.action.SDKVXPAction;
import com.example.integration.hub.action.impl.HangDoiGoiTinActionImpl;
import com.example.integration.hub.action.impl.SDKVXPActionImpl;
import com.example.integration.hub.service.DonViLienThongService;
import com.example.integration.hub.service.TepDuLieuService;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fds.flex.common.ultility.GetterUtil;
import com.fds.flex.common.ultility.ResponseUtil;

import com.fds.flex.common.ultility.Validator;
import com.fds.flex.common.utility.string.StringPool;
import com.vpcp.services.KnobstickService;
import com.vpcp.services.model.*;
import com.vpcp.services.request.AgencyRequest;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;
import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import com.fasterxml.jackson.core.type.TypeReference;


@CrossOrigin(origins = "*")
@RestController
@RequestMapping("/integration/1.0/hangdoigoitin")
@Slf4j
@Validated
public class HangDoiGoiTinController {
    @Autowired
    DonViLienThongService donViLienThongService;
    @Autowired
    HangDoiGoiTinAction action;
    @Autowired
    DonViLienThongAction donViLienThongAction;
    @Autowired
    TepDuLieuService tepDuLieuService;

    private ObjectMapper objectMapper = new ObjectMapper();
    private static final String LOG_FILE_PATH = "logDongBoDVLT.json";

    @PostMapping(value = "sendEdoc", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE, MediaType.APPLICATION_JSON_VALUE}, produces = {
            MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<?> sendEdoc(@RequestParam("File") MultipartFile file, @RequestHeader Map<String, String> headers) {
        JSONObject result = new JSONObject();

        if (file.isEmpty()) {
            return ResponseEntity.badRequest().body("File không được để trống");
        }

        if (!headers.containsKey(Constant.HEADER_MESSAGE_TYPE)) {

            result.put("ErrorCode", "-1");
            result.put("ErrorDesc", "That bai- No messagetype was found");
            return ResponseEntity.badRequest().body(result.toString());
        }

        try {
            TepDuLieu fileSaved = action.uploadFile(file);

            if (Validator.isNull(fileSaved)) {
                throw new Exception("TepDuLieu is null");
            }

            String duongDanFile = fileSaved.getDuongDanURL();

            List<JsonNode> fromToNode = PortalUtil.getEdxmlFromTo(headers.get(Constant.HEADER_MESSAGE_TYPE), duongDanFile);
            if (Validator.isNull(fromToNode) || Validator.isNull(fromToNode.get(0)) || Validator.isNull(fromToNode.get(1))) {

                result.put("ErrorCode", "-1");
                result.put("ErrorDesc", "That bai- EdXml format is error");
                return ResponseEntity.badRequest().body(result.toString());
            }

            HangDoiGoiTinReqDTO request = new HangDoiGoiTinReqDTO();
            HangDoiGoiTinReqDTO.HangDoiGoiTinDTO_HeThongKetNoi noiGuiGoiTin = new HangDoiGoiTinReqDTO.HangDoiGoiTinDTO_HeThongKetNoi();
            List<HangDoiGoiTinReqDTO.HangDoiGoiTinDTO_HeThongKetNoi> noiNhanGoiTins = new ArrayList<>();
            HangDoiGoiTinReqDTO.HangDoiGoiTinDTO_TrucTichHop trucTichHop = new HangDoiGoiTinReqDTO.HangDoiGoiTinDTO_TrucTichHop();
            HangDoiGoiTinReqDTO.HangDoiGoiTinDTO_TepDuLieu noiDungGoiTin = new HangDoiGoiTinReqDTO.HangDoiGoiTinDTO_TepDuLieu();

            JsonNode toNode = fromToNode.get(1);
            JsonNode fromNode = fromToNode.get(0);
            JsonNode organIdNodeFrom = fromNode.get("OrganId");

            if (organIdNodeFrom != null && !organIdNodeFrom.isNull()) {
                noiGuiGoiTin.setMaKetNoi(organIdNodeFrom.asText());
            }
            JsonNode organNameNodeFrom = fromNode.get("OrganName");
            if (organNameNodeFrom != null && !organNameNodeFrom.isNull()) {
                noiGuiGoiTin.setTenKetNoi(organNameNodeFrom.asText());
            }

            HangDoiGoiTinReqDTO.HangDoiGoiTinDTO_HeThongKetNoi noiNhanGoiTin;
            if (toNode.isArray()) {
                for (JsonNode node : toNode) {
                    noiNhanGoiTin = new HangDoiGoiTinReqDTO.HangDoiGoiTinDTO_HeThongKetNoi();
                    noiNhanGoiTin.setMaKetNoi(node.get("OrganId").asText());
                    noiNhanGoiTin.setTenKetNoi(node.get("OrganName").asText());
                    noiNhanGoiTins.add(noiNhanGoiTin);
                }
            } else {
                noiNhanGoiTin = new HangDoiGoiTinReqDTO.HangDoiGoiTinDTO_HeThongKetNoi();
                JsonNode organIdNode = toNode.get("OrganId");
                if (organIdNode != null && !organIdNode.isNull()) {
                    noiNhanGoiTin.setMaKetNoi(organIdNode.asText());
                }
                JsonNode organNameNode = toNode.get("OrganName");
                if (organNameNode != null && !organNameNode.isNull()) {
                    noiNhanGoiTin.setTenKetNoi(organNameNode.asText());
                }
                noiNhanGoiTins.add(noiNhanGoiTin);
            }

            trucTichHop.setMaMuc(Constant.TRUC_TICH_HOP_VAN_BAN_DEFAULT);
            noiDungGoiTin.setMaDinhDanh(fileSaved.getMaDinhDanh());

            request.setNoiGuiGoiTin(noiGuiGoiTin);
            request.setNoiNhanGoiTin(noiNhanGoiTins);
            request.setTrucTichHop(trucTichHop);
            request.setKieuLoaiGoiTin(Constant.SEND_EDOC);
            request.setTepDuLieu(noiDungGoiTin);
            request.setDinhDangGoiTin(headers.get(Constant.HEADER_MESSAGE_TYPE));
            request.setHeaders(headers);

            HangDoiGoiTin hangDoiGoiTin = action.add(request);

            result.put("status", "OK");
            result.put("ErrorCode", "0");
            result.put("ErrorDesc", "Thanh cong");
            result.put("DocId", hangDoiGoiTin.getMaDinhDanh());

            return ResponseEntity.ok(result.toString());
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error("File upload failed: " + ex.getMessage());
            return ResponseEntity.internalServerError().body("File upload failed");
        }
    }

    @GetMapping(value = "/statistic", produces = {
            MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<?> getStatistic(@RequestParam(name = "year") Integer year) {
        ThongKeEdoc result = action.viewThongKe(year);
        return ResponseEntity.ok(result);
    }

    @GetMapping(value = "/statistic/thongkenoiguigoitin", produces = {
            MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<?> getStatisticNoiGuiGoiTin(@RequestParam(name = "kieuLoaiGoiTin") String kieuLoaiGoiTin, @RequestParam(name = "year") int year) {
        ThongKeEdoc result = action.getStatisticNoiGuiGoiTin(kieuLoaiGoiTin, year);
        return ResponseEntity.ok(result);
    }

    @GetMapping(value = "/statistic/thongkelienthongvanban", produces = {
            MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<?> getStatisticThongKeLienThongVanBan(@RequestParam(name = "year") int year) {
        ThongKeLienThongVanBan result = action.getStatisticThongKeLienThongVanBan(year);
        return ResponseEntity.ok(result);
    }

//    @PostMapping(value = "/getAgenciesList", produces = {
//            MediaType.APPLICATION_JSON_VALUE})
//    public ResponseEntity<?> getAgenciesList() {
//
//
//        GetAgencyRespDTO respDTO = new GetAgencyRespDTO();
//
//        List<C_DonViLienThong> donViLienThongs = donViLienThongService.filter();
//
//        List<GetAgencyRespDTO.DataObject> donViLienThongDTOs = donViLienThongs.stream()
//                .map(donVi -> new GetAgencyRespDTO.DataObject(donVi.getMaMuc(), donVi.getTenMuc(), donVi.getMaMuc(),donVi.getPid(),donVi.getID_DVLT()))
//                .collect(Collectors.toList());
//
//            respDTO.setData(donViLienThongDTOs);
//            respDTO.setErrorDesc("Thanh cong");
//            respDTO.setErrorCode("0");
//            respDTO.setStatus("OK");
//        return ResponseEntity.ok().body(respDTO);
//    }
    @PostMapping(value = "/getAgenciesList", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> getAgenciesList() {

        String secretVDXP = GetterUtil.get(PropKey.getKeyMap().get(PropKey.INTEGRATION_HUB_LGSP_SECRET_VDXP), StringPool.BLANK);
        String systemId = GetterUtil.get(PropKey.getKeyMap().get(PropKey.INTEGRATION_HUB_LGSP_SYSTEMID), StringPool.BLANK);
        String hostLgsp = GetterUtil.get(PropKey.getKeyMap().get(PropKey.INTEGRATION_HUB_LGSP_HOSTLGSP), StringPool.BLANK);

        SDKVXPAction sdkVxpAction = new SDKVXPActionImpl(hostLgsp, systemId, secretVDXP);
        GetAgenciesResult agencyResult = sdkVxpAction.getAgenciesList();

        return ResponseEntity.ok(agencyResult);
    }
@PostMapping(value = "/getSentEdocList", produces = MediaType.APPLICATION_JSON_VALUE)
public ResponseEntity<?> getListSentEdoc(@RequestHeader Map<String, String> headers) {
            String secretVDXP = GetterUtil.get(PropKey.getKeyMap().get(PropKey.INTEGRATION_HUB_LGSP_SECRET_VDXP), StringPool.BLANK);
        String systemId = GetterUtil.get(PropKey.getKeyMap().get(PropKey.INTEGRATION_HUB_LGSP_SYSTEMID), StringPool.BLANK);
        String hostLgsp = GetterUtil.get(PropKey.getKeyMap().get(PropKey.INTEGRATION_HUB_LGSP_HOSTLGSP), StringPool.BLANK);
    SDKVXPAction sdkVxpAction = new SDKVXPActionImpl(hostLgsp, systemId, secretVDXP);
    JSONObject result = new JSONObject();

    if (!headers.containsKey("docid") || !headers.containsKey("servicetype") || !headers.containsKey("messagetype")) {
        result.put("code", "-1");
        result.put("message", "Missing required headers: docid, servicetype, messagetype");
        result.put("status", "FAIL");
        return ResponseEntity.badRequest().body(result.toString());
    }

    String docId = headers.get("docid");
    String serviceType = headers.get("servicetype");
    String messageType = headers.get("messagetype");

    try {
        GetSendEdocResult edocResult = sdkVxpAction.getListSentEdoc(serviceType, messageType, docId);

        if (edocResult == null || !"0".equals(edocResult.getErrorCode())) {
            result.put("ErrorCode", edocResult != null ? edocResult.getErrorCode() : "-1");
            result.put("message", edocResult != null ? edocResult.getErrorDesc() : "Unknown error");
            result.put("status", "FAIL");
            return ResponseEntity.badRequest().body(result.toString());
        }

        JSONArray dataArray = new JSONArray();
        for (StatusResult item : edocResult.getStatusResult()) {
            JSONObject oneResult = new JSONObject();
            oneResult.put("serviceType", item.getServiceType());
            oneResult.put("messageType", item.getMessageType());
            oneResult.put("title", item.getTitle());
            oneResult.put("notation", item.getNotation());
            oneResult.put("fromCode", item.getFromCode());
            oneResult.put("senderDocId", item.getSenderDocId());
            oneResult.put("sentTime", item.getSentTime());
            oneResult.put("sendStatus", item.getSendStatus());
            oneResult.put("sendStatusDesc", item.getSendStatusDesc());
            oneResult.put("toCode", item.getToCode());
            oneResult.put("receiverDocId", item.getReceiverDocId());
            oneResult.put("receivedTime", item.getReceivedTime());
            oneResult.put("receiveStatus", item.getReceiveStatus());
            oneResult.put("receiveStatusDesc", item.getReceiveStatusDesc());

            dataArray.put(oneResult);
        }

        result.put("status", "OK");
        result.put("ErrorCode", "0");
        result.put("ErrorDesc", "Thanh cong");
        result.put("data", dataArray);

        return ResponseEntity.ok().body(result.toString());

    } catch (Exception e) {
        e.printStackTrace();
        result.put("ErrorCode", "-1");
        result.put("ErrorDesc", "Internal server error");
        result.put("status", "FAIL");
        return ResponseEntity.internalServerError().body(result.toString());
    }
}

    @GetMapping(value = "/getLogAgenciesList", produces = {
            MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<?> getLogAgenciesList() {
        try {
            return ResponseEntity.ok(readLogs());
        } catch (IOException e) {
            e.printStackTrace();
            return ResponseEntity.internalServerError().body("getLogAgenciesList failed");
        }
    }

    // Hàm đọc file log.json
    private List<Map<String, String>> readLogs() throws IOException {
        String filePath = GetterUtil.getString(PropKey.getKeyMap().get(PropKey.FLEXCORE_PORTAL_WEB_STATIC_RESOURCE_SAVE_DIR));
        File file = new File(filePath + "/" + LOG_FILE_PATH);
        if (!file.exists()) {
            // Nếu file chưa tồn tại, tạo file trống
            objectMapper.writeValue(file, new ArrayList<>());
            return new ArrayList<>();
        }
        // Đọc file log.json
        return objectMapper.readValue(file, new TypeReference<List<Map<String, String>>>() {
        });
    }

    @PostMapping(value = "/getEdoc", produces = {
            MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<?> getEdoc(@RequestHeader Map<String, String> headers) {
        JSONObject result = new JSONObject();
        if (!headers.containsKey("docid")) {
            result.put("ErrorCode", "-1");
            result.put("ErrorDesc", "InvalidArgument");
            result.put("status", "FAIL");
            return ResponseEntity.badRequest().body(result.toString());
        }

        result.put("status", "OK");
        result.put("ErrorCode", "0");
        result.put("ErrorDesc", "Thanh cong");

        String docId = headers.get("docid");
        HangDoiGoiTin object = action.findByMaGoiTin(docId);

        if (Validator.isNull(object)) {
            result.put("ErrorCode", "-1");
            result.put("ErrorDesc", "Edoc not found");
            result.put("status", "FAIL");
            return ResponseEntity.badRequest().body(result.toString());
        }

        Optional<TepDuLieu> noiDungGoiTin = tepDuLieuService.findByMaDinhDanh(object.getNoiDungGoiTin().getMaDinhDanh());

        if (!noiDungGoiTin.isPresent()) {
            result.put("ErrorCode", "-1");
            result.put("ErrorDesc", "TepDulieu is null");
            result.put("status", "FAIL");
            return ResponseEntity.badRequest().body(result.toString());
        }

        try {
            String data = PortalUtil.encodeFileToBase64(noiDungGoiTin.get().getDuongDanURL());
            result.put("data", data);
            return ResponseEntity.ok().body(result.toString());
        } catch (IOException e) {
            log.error("Error when getEdoc with edocId: " + docId);
        }

        result.put("ErrorCode", "-1");
        result.put("ErrorDesc", "data edxml is not valid");
        result.put("status", "FAIL");
        return ResponseEntity.badRequest().body(result.toString());
    }


    @PostMapping(value = "updateStatus", produces = {
            MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<?> updateStatus(@RequestHeader Map<String, String> headers) {
        JSONObject result = new JSONObject();
        if (!headers.containsKey("docid") || !headers.containsKey("status")) {
            result.put("ErrorCode", "-1");
            result.put("ErrorDesc", "InvalidArgument");
            result.put("status", "FAIL");
            return ResponseEntity.badRequest().body(result.toString());
        }

        String docId = headers.get("docid");
        String status = headers.get("status");
        String noiNhanRequest =  GetterUtil.get(PropKey.getKeyMap().get(PropKey.INTEGRATION_HUB_LGSP_SYSTEMID), StringPool.BLANK);

        HangDoiGoiTin hangDoiGoiTin = action.updateTrangThai(docId, status, noiNhanRequest);

        if (Validator.isNull(hangDoiGoiTin)) {
            result.put("ErrorCode", "-1");
            result.put("ErrorDesc", "No receiver");
            result.put("status", "FAIL");
            return ResponseEntity.badRequest().body(result.toString());
        }

        result.put("status", "OK");
        result.put("ErrorCode", "0");
        result.put("ErrorDesc", "Thanh cong");
        return ResponseEntity.ok().body(result.toString());
    }
    @PostMapping(value = "registerAgency", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> registerAgency(@RequestBody AgencyRequest request) {

        String secretVDXP = GetterUtil.get(PropKey.getKeyMap().get(PropKey.INTEGRATION_HUB_LGSP_SECRET_VDXP), StringPool.BLANK);
        String systemId = GetterUtil.get(PropKey.getKeyMap().get(PropKey.INTEGRATION_HUB_LGSP_SYSTEMID), StringPool.BLANK);
        String hostLgsp = GetterUtil.get(PropKey.getKeyMap().get(PropKey.INTEGRATION_HUB_LGSP_HOSTLGSP), StringPool.BLANK);


        SDKVXPAction sdkVxpAction = new SDKVXPActionImpl(hostLgsp, systemId, secretVDXP);
        RegisterAgencyResult agencyResult = sdkVxpAction.registerAgency(request);


        JSONObject result = new JSONObject();
        result.put("status", agencyResult.getStatus());
        result.put("ErrorCode", agencyResult.getErrorCode());
        result.put("ErrorDesc", agencyResult.getErrorDesc());


        if (!"0".equals(agencyResult.getErrorCode())) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(result.toString());
        }
        return ResponseEntity.ok(result.toString());
    }

    @PostMapping(value = "getReceivedEdocList", produces = {
            MediaType.APPLICATION_JSON_VALUE, MediaType.TEXT_PLAIN_VALUE})
    public ResponseEntity<?> getReceivedEdocList(@RequestHeader Map<String, String> headers) {
        JSONObject result = new JSONObject();
        JSONArray data = new JSONArray();

        result.put("ErrorDesc", "Thanh cong");
        result.put("ErrorCode", "0");
        result.put("status", "OK");
        result.put("data", data);

        if (!headers.containsKey(Constant.HEADER_MESSAGE_TYPE)) {
            result.put("ErrorCode", "-1");
            result.put("ErrorDesc", "That bai- No messagetype was found");
            return ResponseEntity.badRequest().body(result.toString());
        }
        String noiNhan = GetterUtil.get(PropKey.getKeyMap().get(PropKey.INTEGRATION_HUB_LGSP_SYSTEMID), StringPool.BLANK);
        String messagetype = headers.get(Constant.HEADER_MESSAGE_TYPE);

        Page<HangDoiGoiTin> hangDoiGoiTinPage = action.filter(null, Constant.TRUC_TICH_HOP_VAN_BAN_DEFAULT, Constant.STATUS_INITIAL,
                null, noiNhan, messagetype, null, 0, 50, null, null);

        if (Validator.isNull(hangDoiGoiTinPage) || hangDoiGoiTinPage.getContent().isEmpty()) {
            log.warn("Not found hangDoiGoiTin for systemId: " + noiNhan);
            return ResponseEntity.ok(result.toString());
        }

        List<HangDoiGoiTin> hangDoiGoiTins = hangDoiGoiTinPage.getContent();
        JSONObject oneData;
        boolean isInit;
        for (HangDoiGoiTin hangDoiGoiTin : hangDoiGoiTins) {
            isInit = true;
            List<HeThongKetNoiExt> noiNhanGoiTins = hangDoiGoiTin.getNoiNhanGoiTin();
            for (HeThongKetNoiExt noiNhanGoiTin : noiNhanGoiTins) {
                if (noiNhan.equals(noiNhanGoiTin.getMaKetNoi())) {
                    if (!noiNhanGoiTin.getTrangThaiLienThong().getMaMuc().equals(Constant.STATUS_INITIAL)) {
                        isInit = false;
                    }
                }
            }

            if (!isInit) {
                continue;
            }

            oneData = new JSONObject();
            oneData.put("serviceType", "eDoc");
            oneData.put("messagetype", hangDoiGoiTin.getDinhDangGoiTin());
            oneData.put("from", Validator.isNotNull(hangDoiGoiTin.getNoiGuiGoiTin()) ? hangDoiGoiTin.getNoiGuiGoiTin().getMaKetNoi() : "");
            oneData.put("to", noiNhan);
            oneData.put("docId", hangDoiGoiTin.getMaGoiTin());
            oneData.put("created_time", Validator.isNotNull(hangDoiGoiTin.getNoiGuiGoiTin()) ? hangDoiGoiTin.getNoiGuiGoiTin().getTimeVdxpCreate() : "");
            oneData.put("updated_time", Validator.isNotNull(hangDoiGoiTin.getNoiGuiGoiTin()) ? hangDoiGoiTin.getNoiGuiGoiTin().getTimeVdxpUpdate() : "");
            data.put(oneData);
        }


        return ResponseEntity.ok(result.toString());
    }


    @PostMapping(value = "getReceivedEdocList1", produces = {
            MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<?> getReceivedEdocList1(@RequestHeader Map<String, String> headers) {
        String secretVDXP = GetterUtil.get(PropKey.getKeyMap().get(PropKey.INTEGRATION_HUB_LGSP_SECRET_VDXP), StringPool.BLANK);
        String systemId = GetterUtil.get(PropKey.getKeyMap().get(PropKey.INTEGRATION_HUB_LGSP_SYSTEMID), StringPool.BLANK);
        String hostLgsp = GetterUtil.get(PropKey.getKeyMap().get(PropKey.INTEGRATION_HUB_LGSP_HOSTLGSP), StringPool.BLANK);
        SDKVXPAction sdkVxpAction = new SDKVXPActionImpl(hostLgsp, systemId, secretVDXP);
        JSONObject result = new JSONObject();
        JSONArray data = new JSONArray();

        result.put("message", "Thanh cong");
        result.put("code", "0");
        result.put("status", "OK");
        result.put("data", data);

        if (!headers.containsKey("servicetype") || !headers.containsKey("messagetype")) {
            result.put("code", "-1");
            result.put("message", "Missing required headers: docid, servicetype, messagetype");
            result.put("status", "FAIL");
            return ResponseEntity.badRequest().body(result.toString());
        }
        String serviceType = headers.get("servicetype");
        String messagetype = headers.get("messagetype");

        try {
            GetReceivedEdocResult edocResult = sdkVxpAction.getListEdoc(serviceType, messagetype);

            if (edocResult == null || !"0".equals(edocResult.getErrorCode())) {
                result.put("ErrorCode", edocResult != null ? edocResult.getErrorCode() : "-1");
                result.put("message", edocResult != null ? edocResult.getErrorDesc() : "Unknown error");
                result.put("status", "FAIL");
                return ResponseEntity.badRequest().body(result.toString());
            }

            JSONArray dataArray = new JSONArray();
            for (Knobstick item : edocResult.getKnobsticks()) {
                JSONObject oneResult = new JSONObject();
                oneResult.put("serviceType", item.getServiceType());
                oneResult.put("created_time", item.getCreatedTime());
                oneResult.put("updated_time", item.getUpdatedTime());
                oneResult.put("message_type", item.getMessageType());
                oneResult.put("status_desc", item.getStatusDesc());
                oneResult.put("docId", item.getId());
                oneResult.put("from", item.getFrom());
                oneResult.put("to", item.getTo());
                oneResult.put("status", item.getStatus());

                dataArray.put(oneResult);
            }

            result.put("status", "OK");
            result.put("code", "0");
            result.put("message", "Thanh cong");
            result.put("data", dataArray);

            return ResponseEntity.ok().body(result.toString());

        } catch (Exception e) {
            e.printStackTrace();
            result.put("ErrorCode", "-1");
            result.put("ErrorDesc", "Internal server error");
            result.put("status", "FAIL");
            return ResponseEntity.internalServerError().body(result.toString());
        }


    }
    @PostMapping(value = "", consumes = {MediaType.APPLICATION_JSON_VALUE, MediaType.TEXT_PLAIN_VALUE}, produces = {
            MediaType.APPLICATION_JSON_VALUE, MediaType.TEXT_PLAIN_VALUE})
    public ResponseEntity<?> add(@RequestBody HangDoiGoiTinReqDTO request, @RequestHeader Map<String, String> headers) {
        headers.entrySet().removeIf(entry -> !entry.getKey().equalsIgnoreCase(Constant.X_LGSP_FROM)
                && !entry.getKey().equalsIgnoreCase(Constant.X_LGSP_TO)
                && !entry.getKey().equalsIgnoreCase(Constant.FROM));

        request.setHeaders(headers);

        HangDoiGoiTin object = action.add(request);

        return ResponseEntity.ok(new HangDoiGoiTinRespDTO(ResponseUtil.RespCode.SUCCESS.getCode(), "success", "success",
                request, object));
    }

    @PostMapping(value = "/upload/file", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE}, produces = {
            MediaType.APPLICATION_JSON_VALUE, MediaType.TEXT_PLAIN_VALUE})
    public ResponseEntity<?> uploadFile(@RequestParam("file") MultipartFile file) {

        if (file.isEmpty()) {
            return ResponseEntity.badRequest().body("Please upload a file.");
        }

        try {
            TepDuLieu fileSaved = action.uploadFile(file);
            fileSaved.setDuongDanURL("----");
            return ResponseEntity.ok(fileSaved);
        } catch (Exception ex) {
            return ResponseEntity.internalServerError().body("File upload failed: " + ex.getMessage());
        }
    }

    @PostMapping(value = "/view/file/{id}")
    public ResponseEntity<?> viewFile(@PathVariable String id) {
        try {
            File file = action.viewFile(id);
            if (Validator.isNull(file)) {
                return ResponseEntity.badRequest().body("file is null");
            }

            FileSystemResource resource = new FileSystemResource(file);

            // Set headers to indicate a file download
            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + file.getName());
            headers.add(HttpHeaders.CONTENT_TYPE, "application/octet-stream");  // You can set the proper MIME type here

            // Return the file with 200 OK status
            return ResponseEntity.ok()
                    .headers(headers)
                    .body(resource);
        } catch (Exception ex) {
            return ResponseEntity.internalServerError().body("View file failed: " + ex.getMessage());
        }
    }

    @PutMapping(value = "/{id}", consumes = {MediaType.APPLICATION_JSON_VALUE,
            MediaType.TEXT_PLAIN_VALUE}, produces = {MediaType.APPLICATION_JSON_VALUE, MediaType.TEXT_PLAIN_VALUE})
    public ResponseEntity<?> updateTrangThai(@PathVariable(name = "id") String id, @RequestBody HangDoiGoiTinTrangThaiReqDTO request) throws Exception {

        log.debug("Get body update HangDoiGoiTin: {} ", new JSONObject(request));

        HangDoiGoiTin object = action.update(id, request);

        return ResponseEntity.ok(object);

    }

    // @PreAuthorize("hasPermission('HangDoiGoiTin','UPDATE')")
    @PostMapping(value = "/{id}", consumes = {MediaType.APPLICATION_JSON_VALUE,
            MediaType.TEXT_PLAIN_VALUE}, produces = {MediaType.APPLICATION_JSON_VALUE, MediaType.TEXT_PLAIN_VALUE})
    public ResponseEntity<?> update(@PathVariable(name = "id") String id, @RequestBody HangDoiGoiTinReqDTO request) {

        log.debug("Get body update HangDoiGoiTin: {} ", new JSONObject(request));

        HangDoiGoiTin object = action.update(id, request);

        return ResponseEntity.ok(new HangDoiGoiTinRespDTO(ResponseUtil.RespCode.SUCCESS.getCode(), "success", "success",
                request, object));

    }

    // @PreAuthorize("hasPermission('HangDoiGoiTin','READ')")
    @GetMapping(value = "/{id}")
    public ResponseEntity<?> findById(@PathVariable(name = "id") String id) {

        log.debug("Find HangDoiGoiTin: {} ", id);

        HangDoiGoiTin object = action.findById(id);

        return ResponseEntity.ok(
                new HangDoiGoiTinRespDTO(ResponseUtil.RespCode.SUCCESS.getCode(), "success", "success", null, object));

    }

    // @PreAuthorize("hasPermission('HangDoiGoiTin','DELETE')")
    @DeleteMapping(value = "/{id}")
    public ResponseEntity<?> delete(@PathVariable(name = "id") String id) {

        log.debug("Delete HangDoiGoiTin: {} ", id);

        action.delete(id);

        return ResponseEntity.ok(new BaseRespDTO<String, String>(ResponseUtil.RespCode.SUCCESS.getCode(), "success",
                "success", id, "Delete success"));

    }

    // @PreAuthorize("hasPermission('HangDoiGoiTin','READ')")
    @GetMapping(value = "/filter")
    public ResponseEntity<?> filter(
            @RequestParam(name = "keyword", required = false) String keyword,
            @RequestParam(name = "trucTichHop") String trucTichHop,
            @RequestParam(name = "trangThai", required = false) String trangThai,
            @RequestParam(name = "kieuLoaiGoiTin", required = false) String kieuLoaiGoiTin,
            @RequestParam(name = "noiGui", required = false) String noiGui,
            @RequestParam(name = "noiNhan", required = false) String noiNhan,
            @RequestParam(name = "dinhDangGoiTin", required = false) String dinhDangGoiTin,
            @RequestParam(name = "page") Integer page,
            @RequestParam(name = "size") Integer size,
            @RequestParam(name = "orderFields", required = false, defaultValue = "ThoiGianCapNhat") String orderFields,
            @RequestParam(name = "orderTypes", required = false, defaultValue = "desc") String orderTypes) {

        log.debug(
                "Filter HangDoiGoiTin: trucTichHop: {}, noiGui: {}, noiNhan: {}, page:{}, size:{}, orderFields:{}, orderTypes:{}",
                trucTichHop, noiGui, noiNhan, page, size, orderFields, orderTypes);

        Page<HangDoiGoiTin> result = action.filter(keyword, trucTichHop, trangThai, noiGui, noiNhan, dinhDangGoiTin, kieuLoaiGoiTin, page, size, orderFields,
                orderTypes);

        return ResponseEntity.ok(result);
    }
    @GetMapping(value = "/export")
    public ResponseEntity<?> export(@RequestParam(name = "keyword") String keyword,
                                    @RequestParam(name = "trucTichHop") String trucTichHop,
                                    @RequestParam(name = "trangThai", required = false) String trangThai,
                                    @RequestParam(name = "kieuLoaiGoiTin", required = false) String kieuLoaiGoiTin,
                                    @RequestParam(name = "noiGui", required = false) String noiGui,
                                    @RequestParam(name = "noiNhan", required = false) String noiNhan, @RequestParam(name = "page") Integer page,
                                    @RequestParam(name = "size") Integer size,
                                    @RequestParam(name = "orderFields", required = false, defaultValue = "ThoiGianCapNhat") String orderFields,
                                    @RequestParam(name = "orderTypes", required = false, defaultValue = "desc") String orderTypes) {

        log.debug(
                "Export HangDoiGoiTin: trucTichHop: {}, noiGui: {}, noiNhan: {}, page:{}, size:{}, orderFields:{}, orderTypes:{}",
                trucTichHop, noiGui, noiNhan, page, size, orderFields, orderTypes);

        Page<HangDoiGoiTin> result = action.filter(keyword, trucTichHop, trangThai, noiGui, noiNhan, null, kieuLoaiGoiTin, page, size, orderFields,
                orderTypes);

        return ResponseEntity.ok(result);
    }
}
